"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/maintenance-status-activity/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper functions for generating initials (similar to maintenance list)\nconst getCrewInitials = (assignedTo)=>{\n    var _names__charAt, _names_, _names__charAt1, _names_1;\n    if (!assignedTo) return \"??\";\n    const names = assignedTo.trim().split(\" \");\n    if (names.length === 1) {\n        return names[0].substring(0, 2).toUpperCase();\n    }\n    const first = ((_names_ = names[0]) === null || _names_ === void 0 ? void 0 : (_names__charAt = _names_.charAt(0)) === null || _names__charAt === void 0 ? void 0 : _names__charAt.toUpperCase()) || \"\";\n    const last = ((_names_1 = names[names.length - 1]) === null || _names_1 === void 0 ? void 0 : (_names__charAt1 = _names_1.charAt(0)) === null || _names__charAt1 === void 0 ? void 0 : _names__charAt1.toUpperCase()) || \"\";\n    return \"\".concat(first).concat(last) || \"??\";\n};\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Helper function to get status color classes (similar to maintenance list)\nconst getStatusColorClasses = (status)=>{\n    switch(status){\n        case \"High\":\n            return \"text-destructive hover:text-cinnabar-800\";\n        case \"Upcoming\":\n            return \"text-warning hover:text-fire-bush-500\";\n        default:\n            return \"hover:text-curious-blue-400\";\n    }\n};\n// Function to create columns (will be called inside component to access bp and vessel data)\nconst createMaintenanceReportColumns = (bp, getVesselWithIcon)=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Task Name\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus;\n                const item = row.original;\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const taskContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"leading-tight truncate font-medium\", getStatusColorClasses(overDueStatus)),\n                    children: item.taskName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 21\n                }, undefined);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5\",\n                            children: [\n                                item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                        className: \"text-xs\",\n                                        children: getCrewInitials(item.assignedTo)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid\",\n                                    children: [\n                                        taskContent,\n                                        item.inventoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hover:text-curious-blue-400 text-sm\",\n                                                children: item.inventoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 41\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 37\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden tablet-sm:block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: taskContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"md:hidden\",\n                                        children: item.vesselName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-outer-space-400\",\n                                                    children: [\n                                                        \"Location:\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 45\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hover:text-curious-blue-400\",\n                                                    children: item.vesselName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.taskName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.taskName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"inventoryName\",\n            header: \"Inventory\",\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-lg\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.inventoryName ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"hover:text-curious-blue-400\",\n                        children: item.inventoryName\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.inventoryName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.inventoryName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Location\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                // Create a vessel object for VesselIcon (we don't have vessel ID in report data)\n                const vesselForIcon = {\n                    id: 0,\n                    title: item.vesselName\n                };\n                const vesselWithIcon = getVesselWithIcon(0, vesselForIcon);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.vesselName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-fit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                vessel: vesselWithIcon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.TooltipContent, {\n                                        children: item.vesselName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, item.vesselName, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hover:text-curious-blue-400\",\n                                children: item.vesselName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.vesselName) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.vesselName) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"assignedTo\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Assigned\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2.5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.Avatar, {\n                                className: \"h-8 w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_13__.AvatarFallback, {\n                                    className: \"text-xs\",\n                                    children: getCrewInitials(item.assignedTo)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hover:text-curious-blue-400 hidden tablet-md:block\",\n                                children: item.assignedTo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.assignedTo) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.assignedTo) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.status || \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.status) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.status) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"dueDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Date\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            breakpoint: \"tablet-sm\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"-\"\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.dueDate) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.dueDate) || \"\";\n                return dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueA).unix() - dayjs__WEBPACK_IMPORTED_MODULE_4___default()(valueB).unix();\n            }\n        },\n        {\n            accessorKey: \"dueStatus\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Due Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cell: (param)=>{\n                let { row } = param;\n                var _item_dueStatus, _item_dueStatus1;\n                const item = row.original;\n                const maintenanceCheck = createMaintenanceCheckForBadge(item);\n                if (!maintenanceCheck) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"-\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 28\n                    }, undefined);\n                }\n                const overDueStatus = (_item_dueStatus = item.dueStatus) === null || _item_dueStatus === void 0 ? void 0 : _item_dueStatus.status;\n                const overDueDays = (_item_dueStatus1 = item.dueStatus) === null || _item_dueStatus1 === void 0 ? void 0 : _item_dueStatus1.days;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: overDueStatus === \"High\" ? !bp[\"tablet-sm\"] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1\",\n                        children: overDueDays || \"Overdue\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 33\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 33\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_10__.StatusBadge, {\n                        maintenanceCheck: maintenanceCheck\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_dueStatus, _rowA_original, _rowB_original_dueStatus, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_dueStatus = _rowA_original.dueStatus) === null || _rowA_original_dueStatus === void 0 ? void 0 : _rowA_original_dueStatus.days) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_dueStatus = _rowB_original.dueStatus) === null || _rowB_original_dueStatus === void 0 ? void 0 : _rowB_original_dueStatus.days) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n// Row status evaluator for maintenance report (similar to maintenance list)\nconst getMaintenanceReportRowStatus = (reportItem)=>{\n    var _reportItem_dueStatus;\n    // Skip completed, archived, or draft tasks\n    if (reportItem.status === \"Completed\" || reportItem.status === \"Save_As_Draft\") {\n        return \"normal\";\n    }\n    const overDueStatus = (_reportItem_dueStatus = reportItem.dueStatus) === null || _reportItem_dueStatus === void 0 ? void 0 : _reportItem_dueStatus.status;\n    // Use the pre-calculated status values from the system\n    switch(overDueStatus){\n        case \"High\":\n            return \"overdue\" // Red highlighting\n            ;\n        case \"Upcoming\":\n            return \"upcoming\" // Orange highlighting\n            ;\n        case \"Medium\":\n        case \"Open\":\n        default:\n            return \"normal\" // No highlighting\n            ;\n    }\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints)();\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: null,\n        endDate: null\n    });\n    // Create columns with access to bp and vessel icon data\n    const columns = createMaintenanceReportColumns(bp, getVesselWithIcon);\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readComponentMaintenanceChecks_nodes, _data_readComponentMaintenanceChecks;\n            console.log(\"✅ GraphQL query completed successfully:\", data);\n            console.log(\"\\uD83D\\uDCCA Number of records returned:\", (data === null || data === void 0 ? void 0 : (_data_readComponentMaintenanceChecks = data.readComponentMaintenanceChecks) === null || _data_readComponentMaintenanceChecks === void 0 ? void 0 : (_data_readComponentMaintenanceChecks_nodes = _data_readComponentMaintenanceChecks.nodes) === null || _data_readComponentMaintenanceChecks_nodes === void 0 ? void 0 : _data_readComponentMaintenanceChecks_nodes.length) || 0);\n        },\n        onError: (error)=>{\n            console.error(\"❌ GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error:\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D Filter change detected:\", {\n            type,\n            data\n        });\n        switch(type){\n            case \"vessels\":\n                console.log(\"\\uD83D\\uDEA2 Setting vessels:\", data);\n                setSelectedVessels(data);\n                break;\n            case \"category\":\n                console.log(\"\\uD83D\\uDCC2 Setting category:\", data);\n                setCategory(data);\n                break;\n            case \"status\":\n                console.log(\"\\uD83D\\uDCCA Setting status:\", data);\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                console.log(\"\\uD83D\\uDCC5 Setting date range:\", data);\n                setDateRange(data || {\n                    startDate: null,\n                    endDate: null\n                });\n                break;\n            case \"member\":\n                console.log(\"\\uD83D\\uDC64 Setting crew:\", data);\n                setCrew(data);\n                break;\n            default:\n                console.log(\"❓ Unknown filter type:\", type, data);\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        console.log(\"\\uD83D\\uDD04 Generating report with current state:\");\n        console.log(\"  \\uD83D\\uDCC5 Date range:\", dateRange);\n        console.log(\"  \\uD83D\\uDEA2 Selected vessels:\", selectedVessels);\n        console.log(\"  \\uD83D\\uDCC2 Category:\", category);\n        console.log(\"  \\uD83D\\uDCCA Status:\", status);\n        console.log(\"  \\uD83D\\uDC64 Crew:\", crew);\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n            console.log(\"  ✅ Added date filter:\", filter[\"expires\"]);\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>item.value)\n            };\n            console.log(\"  ✅ Added vessel filter:\", filter[\"basicComponentID\"]);\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: +category.value\n            };\n            console.log(\"  ✅ Added category filter:\", filter[\"maintenanceCategoryID\"]);\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n            console.log(\"  ✅ Added status filter:\", filter[\"status\"]);\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: crew.value\n            };\n            console.log(\"  ✅ Added crew filter:\", filter[\"assignedToID\"]);\n        }\n        console.log(\"\\uD83D\\uDE80 Final filter object:\", filter);\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 654,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 651,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getMaintenanceReportRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 661,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 660,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"qVt0ywjp5muU0SU2GNcNwRT8Hvw=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_14__.useBreakpoints,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_16__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});