"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@fullcalendar+react@6.1.18__d4594dd1ac376bd9b9213186a527adb9";
exports.ids = ["vendor-chunks/@fullcalendar+react@6.1.18__d4594dd1ac376bd9b9213186a527adb9"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@fullcalendar+react@6.1.18__d4594dd1ac376bd9b9213186a527adb9/node_modules/@fullcalendar/react/dist/index.js":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@fullcalendar+react@6.1.18__d4594dd1ac376bd9b9213186a527adb9/node_modules/@fullcalendar/react/dist/index.js ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FullCalendar)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _fullcalendar_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @fullcalendar/core */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+core@6.1.18/node_modules/@fullcalendar/core/index.js\");\n/* harmony import */ var _fullcalendar_core_internal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fullcalendar/core/internal */ \"(ssr)/./node_modules/.pnpm/@fullcalendar+core@6.1.18/node_modules/@fullcalendar/core/internal-common.js\");\n\n\n\n\nconst reactMajorVersion = parseInt(String(react__WEBPACK_IMPORTED_MODULE_0__.version).split('.')[0]);\nconst syncRenderingByDefault = reactMajorVersion < 18;\nclass FullCalendar extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor() {\n        super(...arguments);\n        this.elRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)();\n        this.isUpdating = false;\n        this.isUnmounting = false;\n        this.state = {\n            customRenderingMap: new Map()\n        };\n        this.requestResize = () => {\n            if (!this.isUnmounting) {\n                this.cancelResize();\n                this.resizeId = requestAnimationFrame(() => {\n                    this.doResize();\n                });\n            }\n        };\n    }\n    render() {\n        const customRenderingNodes = [];\n        for (const customRendering of this.state.customRenderingMap.values()) {\n            customRenderingNodes.push(react__WEBPACK_IMPORTED_MODULE_0__.createElement(CustomRenderingComponent, { key: customRendering.id, customRendering: customRendering }));\n        }\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: this.elRef }, customRenderingNodes));\n    }\n    componentDidMount() {\n        // reset b/c react strict-mode calls componentWillUnmount/componentDidMount\n        this.isUnmounting = false;\n        const customRenderingStore = new _fullcalendar_core_internal__WEBPACK_IMPORTED_MODULE_2__.cv();\n        this.handleCustomRendering = customRenderingStore.handle.bind(customRenderingStore);\n        this.calendar = new _fullcalendar_core__WEBPACK_IMPORTED_MODULE_3__.Calendar(this.elRef.current, Object.assign(Object.assign({}, this.props), { handleCustomRendering: this.handleCustomRendering }));\n        this.calendar.render();\n        // attaching with .on() will cause this to fire AFTER internal preact rendering did flushSync\n        this.calendar.on('_beforeprint', () => {\n            (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n                // our `customRenderingMap` state will be flushed at this point\n            });\n        });\n        let lastRequestTimestamp;\n        customRenderingStore.subscribe((customRenderingMap) => {\n            const requestTimestamp = Date.now();\n            const isMounting = !lastRequestTimestamp;\n            const runFunc = (\n            // don't call flushSync if React version already does sync rendering by default\n            // guards against fatal errors:\n            // https://github.com/fullcalendar/fullcalendar/issues/7448\n            syncRenderingByDefault ||\n                //\n                isMounting ||\n                this.isUpdating ||\n                this.isUnmounting ||\n                (requestTimestamp - lastRequestTimestamp) < 100 // rerendering frequently\n            ) ? runNow // either sync rendering (first-time or React 16/17) or async (React 18)\n                : react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync; // guaranteed sync rendering\n            runFunc(() => {\n                this.setState({ customRenderingMap }, () => {\n                    lastRequestTimestamp = requestTimestamp;\n                    if (isMounting) {\n                        this.doResize();\n                    }\n                    else {\n                        this.requestResize();\n                    }\n                });\n            });\n        });\n    }\n    componentDidUpdate() {\n        this.isUpdating = true;\n        this.calendar.resetOptions(Object.assign(Object.assign({}, this.props), { handleCustomRendering: this.handleCustomRendering }));\n        this.isUpdating = false;\n    }\n    componentWillUnmount() {\n        this.isUnmounting = true;\n        this.cancelResize();\n        this.calendar.destroy();\n    }\n    doResize() {\n        this.calendar.updateSize();\n    }\n    cancelResize() {\n        if (this.resizeId !== undefined) {\n            cancelAnimationFrame(this.resizeId);\n            this.resizeId = undefined;\n        }\n    }\n    getApi() {\n        return this.calendar;\n    }\n}\nFullCalendar.act = runNow; // DEPRECATED. Not leveraged anymore\nclass CustomRenderingComponent extends react__WEBPACK_IMPORTED_MODULE_0__.PureComponent {\n    render() {\n        const { customRendering } = this.props;\n        const { generatorMeta } = customRendering;\n        const vnode = typeof generatorMeta === 'function' ?\n            generatorMeta(customRendering.renderProps) :\n            generatorMeta;\n        return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(vnode, customRendering.containerEl);\n    }\n}\n// Util\n// -------------------------------------------------------------------------------------------------\nfunction runNow(f) {\n    f();\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@fullcalendar+react@6.1.18__d4594dd1ac376bd9b9213186a527adb9/node_modules/@fullcalendar/react/dist/index.js\n");

/***/ })

};
;