'use client'

import { GET_MAINTENANCE_CATEGORY } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import { Combobox } from '@/components/ui/comboBox'

const MaintenanceCategoryDropdown = ({
    value,
    onChange,
    isClearable = false,
}: any) => {
    const [isLoading, setIsLoading] = useState(true)
    const [categoryList, setCategoryList] = useState([] as any)
    const [selectedCategory, setSelectedCategory] = useState([] as any)
    const [queryCategoryList, { loading: queryCategoryListLoading }] =
        useLazyQuery(GET_MAINTENANCE_CATEGORY, {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readMaintenanceCategories.nodes
                console.log('📂 MaintenanceCategories loaded:', data)

                if (data) {
                    const filteredData = data.filter(
                        (category: any) => !category.archived,
                    )
                    const formattedData = filteredData.map((category: any) => ({
                        value: category.id,
                        label: category.name || 'No Name',
                    }))
                    formattedData.sort((a: any, b: any) =>
                        a.label.localeCompare(b.label),
                    )
                    console.log('📂 Formatted category data:', formattedData)
                    setCategoryList(formattedData)
                    setSelectedCategory(
                        formattedData.find(
                            (category: any) => category.value === value,
                        ),
                    )
                }
            },
            onError: (error: any) => {
                console.error('queryCategoryList error', error)
            },
        })
    const loadCategoryList = async () => {
        await queryCategoryList({
            variables: {
                clientID: +(localStorage.getItem('clientId') ?? 0),
            },
        })
    }
    useEffect(() => {
        if (isLoading) {
            loadCategoryList()
            setIsLoading(false)
        }
    }, [isLoading])
    useEffect(() => {
        setSelectedCategory(
            categoryList.find((category: any) => category.value === value),
        )
    }, [value])
    return (
        <>
            {!isLoading && (
                <Combobox
                    options={categoryList}
                    value={selectedCategory}
                    onChange={(selectedOption: any) => {
                        console.log(
                            '📂 MaintenanceCategoryDropdown onChange:',
                            selectedOption,
                        )
                        setSelectedCategory(selectedOption)
                        onChange(selectedOption)
                    }}
                    isLoading={queryCategoryListLoading}
                    title="Category"
                    placeholder="Category"
                />
            )}
        </>
    )
}

export default MaintenanceCategoryDropdown
