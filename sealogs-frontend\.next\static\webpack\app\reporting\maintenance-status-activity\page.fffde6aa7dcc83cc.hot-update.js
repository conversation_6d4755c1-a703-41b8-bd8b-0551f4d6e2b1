"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/maintenance-status-activity/page",{

/***/ "(app-pages-browser)/./src/components/filter/components/vessel-dropdown.tsx":
/*!**************************************************************!*\
  !*** ./src/components/filter/components/vessel-dropdown.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst VesselDropdown = (param)=>{\n    let { value, onChange, isClearable = false, className = \"\", vesselIdOptions = [], filterByTrainingSessionMemberId = 0, isMulti = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [vesselList, setVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [allVesselList, setAllVesselList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [selectedVessel, setSelectedVessel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [rawVesselData, setRawVesselData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { getVesselWithIcon, loading: vesselIconLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    const [queryVesselList, { loading: queryVesselListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_5__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.VESSEL_BRIEF_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readVessels.nodes;\n            if (data) {\n                const filteredData = data.filter((vessel)=>!vessel.archived && vessel.title);\n                setRawVesselData(filteredData);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselList error\", error);\n        }\n    });\n    const loadVesselList = async ()=>{\n        let filter = {};\n        if (filterByTrainingSessionMemberId > 0) {\n            filter = {\n                trainingSessions: {\n                    members: {\n                        id: {\n                            contains: filterByTrainingSessionMemberId\n                        }\n                    }\n                }\n            };\n        }\n        filter = {\n            ...filter,\n            archived: {\n                eq: false\n            }\n        };\n        queryVesselList({\n            variables: {\n                filter: filter\n            }\n        });\n    };\n    // Process raw vessel data when vessel icon data is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (rawVesselData.length > 0 && !vesselIconLoading) {\n            const formattedData = rawVesselData.map((vessel)=>{\n                const vesselWithIcon = getVesselWithIcon(vessel.id, vessel);\n                return {\n                    value: vessel.id,\n                    label: vessel.title,\n                    vessel: vesselWithIcon\n                };\n            });\n            formattedData.sort((a, b)=>a.label.localeCompare(b.label));\n            setAllVesselList(formattedData);\n            setVesselList(formattedData);\n        }\n    }, [\n        rawVesselData,\n        vesselIconLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselList();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (vesselList.length > 0) {\n            setSelectedVessel(vesselList.find((vessel)=>vessel.value === value));\n        }\n    }, [\n        value,\n        vesselList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (vesselIdOptions.length > 0) {\n            const filteredVesselList = allVesselList.filter((v)=>vesselIdOptions.includes(v.value));\n            setVesselList(filteredVesselList);\n        } else {\n            // If no options are provided, show the full list\n            setVesselList(allVesselList);\n        }\n    }, [\n        vesselIdOptions,\n        allVesselList\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_3__.Combobox, {\n        options: vesselList,\n        defaultValues: selectedVessel,\n        onChange: (selectedOption)=>{\n            console.log(\"\\uD83D\\uDEA2 VesselDropdown onChange:\", selectedOption, \"isMulti:\", isMulti);\n            setSelectedVessel(selectedOption);\n            onChange(selectedOption);\n        },\n        isLoading: queryVesselListLoading && vesselList && !isLoading,\n        title: \"Vessel\",\n        buttonClassName: className,\n        labelClassName: className,\n        placeholder: \"Vessel\",\n        multi: isMulti\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\vessel-dropdown.tsx\",\n        lineNumber: 109,\n        columnNumber: 9\n    }, undefined);\n};\n_s(VesselDropdown, \"vWAYCxyCkfs5FELn4qT8VGaSnhM=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_5__.useLazyQuery\n    ];\n});\n_c = VesselDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (VesselDropdown);\nvar _c;\n$RefreshReg$(_c, \"VesselDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/filter/components/vessel-dropdown.tsx\n"));

/***/ })

});